# OCRFlux MCP 服务器配置文件

# VLLM 服务配置
vllm:
  url: "http://*************:8010"  # VLLM 服务地址
  model: "OCRFlux-3B"           # 模型名称

# MCP 服务器配置
mcp:
  transport: "sse"            # 传输方式: stdio, sse
  
# FastAPI 服务器配置
fastapi:
  host: localhost            # 监听地址
  port: 8080                  # 监听端口
  workers: 1                  # 工作进程数
  
# OCR 解析配置
ocr:
  max_page_retries: 3         # 每页最大重试次数
  skip_cross_page_merge: false # 是否跳过跨页合并
  target_longest_image_dim: 1024 # 图像最长边尺寸
  
# 日志配置
logging:
  level: "INFO"               # 日志级别: DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
# 安全配置
security:
  cors_origins: ["*"]         # CORS 允许的源
  max_file_size: 100          # 最大文件大小 (MB)
  allowed_extensions: [".pdf", ".png", ".jpg", ".jpeg", ".tiff", ".bmp"]
