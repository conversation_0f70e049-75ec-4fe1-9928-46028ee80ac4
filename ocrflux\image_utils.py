import io
import fitz  # PyMuPDF
from PIL import Image
    

def get_page_image(pdf_path, page_number, target_longest_image_dim=None, image_rotation=0):
    # 打开 PDF 文件
    pdf_document = fitz.open(pdf_path)
    
    # 获取指定页面（fitz 页面编号从 0 开始，所以需要 page_number-1）
    page = pdf_document.load_page(page_number - 1)  # page_number 从 1 开始
    
    # 渲染该页为图像（默认为 RGB 模式）
    pix = page.get_pixmap(dpi=144)  # 144 DPI 可以调整图像清晰度
    
    # 转换为 PIL 图像
    image = Image.open(io.BytesIO(pix.tobytes()))

    # 旋转图像（如果需要）
    if image_rotation != 0:
        image = image.rotate(-image_rotation, expand=True)

    # 如果需要缩放图像
    if target_longest_image_dim is not None:
        width, height = image.size
        if width > height:
            new_width = target_longest_image_dim
            new_height = int(height * (target_longest_image_dim / width))
        else:
            new_height = target_longest_image_dim
            new_width = int(width * (target_longest_image_dim / height))
        image = image.resize((new_width, new_height))

    return image

def is_image(file_path):
    try:
        Image.open(file_path)
        return True
    except:
        return False
