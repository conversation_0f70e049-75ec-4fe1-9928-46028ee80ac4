"""
OCRFlux MCP 工具模块
提供带进度跟踪的 OCR 功能
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, Callable, AsyncGenerator
from pathlib import Path
import fitz

from .inference import parse_async, process_task, build_page_to_markdown_query, build_element_merge_detect_query, build_html_table_merge_query, bulid_document_text
from .prompts import PageResponse
from .table_format import table_matrix2html

logger = logging.getLogger(__name__)

class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, callback: Optional[Callable] = None):
        self.callback = callback
        self.total_steps = 0
        self.current_step = 0
        
    def set_total_steps(self, total: int):
        """设置总步数"""
        self.total_steps = total
        self.current_step = 0
        
    def update(self, step: int, message: str = ""):
        """更新进度"""
        self.current_step = step
        progress = self.current_step / self.total_steps if self.total_steps > 0 else 0
        
        if self.callback:
            self.callback(progress, message)
            
    def increment(self, message: str = ""):
        """递增进度"""
        self.update(self.current_step + 1, message)

async def parse_with_progress(
    url: str, 
    model: str, 
    file_path: str, 
    skip_cross_page_merge: bool = False, 
    max_page_retries: int = 1,
    progress_callback: Optional[Callable] = None
) -> Optional[Dict[str, Any]]:
    """
    带进度跟踪的文档解析函数
    
    Args:
        url: VLLM 服务 URL
        model: 模型名称
        file_path: 文档路径
        skip_cross_page_merge: 是否跳过跨页合并
        max_page_retries: 最大重试次数
        progress_callback: 进度回调函数 (progress: float, message: str) -> None
        
    Returns:
        解析结果字典或 None
    """
    tracker = ProgressTracker(progress_callback)
    
    try:
        # 检查文件
        if not Path(file_path).exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        tracker.update(0, f"开始解析文档: {file_path}")
        
        # 获取页数
        if file_path.lower().endswith(".pdf"):
            try:
                reader = fitz.open(file_path)
                num_pages = len(reader)
                reader.close()
            except Exception as e:
                tracker.update(0, f"无法读取PDF文件: {e}")
                return None
        else:
            num_pages = 1
            
        tracker.update(0.1, f"文档包含 {num_pages} 页")
        
        # 计算总步数
        total_steps = num_pages  # 页面解析
        if not skip_cross_page_merge and num_pages > 1:
            total_steps += (num_pages - 1)  # 元素合并检测
            total_steps += 1  # 表格合并（估算）
        total_steps += 1  # 最终构建
        
        tracker.set_total_steps(total_steps)
        
        # Stage 1: 页面到 Markdown 转换
        tracker.update(1, "开始页面解析...")
        page_to_markdown_tasks = []
        results = []
        
        async with asyncio.TaskGroup() as tg:
            for page_num in range(1, num_pages + 1):
                task = tg.create_task(
                    process_task_with_progress(
                        url, model, 'page_to_markdown', 
                        (file_path, page_num), 
                        max_page_retries,
                        lambda p, m, pn=page_num: tracker.update(
                            pn, f"正在解析第 {pn} 页: {m}"
                        )
                    )
                )
                page_to_markdown_tasks.append(task)

        results = [task.result() for task in page_to_markdown_tasks]
        
        page_to_markdown_result = {}
        for i, result in enumerate(results):
            if result is not None:
                page_to_markdown_result[i+1] = result
                
        tracker.update(num_pages, f"页面解析完成，成功解析 {len(page_to_markdown_result)} 页")
        
        # 构建页面文本
        page_texts = {}
        fallback_pages = []
        for page_number in range(1, num_pages+1):
            if page_number not in page_to_markdown_result.keys():
                fallback_pages.append(page_number-1)
            else:
                page_texts[str(page_number-1)] = "\n\n".join(page_to_markdown_result[page_number])

        if skip_cross_page_merge:
            document_text_list = []
            for i in range(num_pages):
                if i not in fallback_pages:
                    document_text_list.append(page_texts[str(i)])
            document_text = "\n\n".join(document_text_list)
            
            tracker.update(total_steps, "解析完成（跳过跨页合并）")
            return {
                "orig_path": file_path,
                "num_pages": num_pages,
                "document_text": document_text,
                "page_texts": page_texts,
                "fallback_pages": fallback_pages,
            }

        # Stage 2: 元素合并检测
        current_step = num_pages
        tracker.update(current_step, "开始跨页元素合并检测...")
        
        element_merge_detect_keys = []
        element_merge_detect_tasks = []
        
        async with asyncio.TaskGroup() as tg:
            for page_num in range(1, num_pages):
                if page_num in page_to_markdown_result.keys() and page_num+1 in page_to_markdown_result.keys():
                    element_merge_detect_keys.append((page_num, page_num+1))
                    task = tg.create_task(
                        process_task_with_progress(
                            url, model, 'element_merge_detect',
                            (page_to_markdown_result[page_num], page_to_markdown_result[page_num+1]),
                            max_page_retries,
                            lambda p, m, pn=page_num: tracker.update(
                                current_step + pn, f"检测第 {pn}-{pn+1} 页元素合并: {m}"
                            )
                        )
                    )
                    element_merge_detect_tasks.append(task)

        results = [task.result() for task in element_merge_detect_tasks]
        
        element_merge_detect_result = {}
        for key, result in zip(element_merge_detect_keys, results):
            if result is not None:
                element_merge_detect_result[key] = result
                
        current_step += len(element_merge_detect_keys)
        tracker.update(current_step, "元素合并检测完成")

        # Stage 3: HTML 表格合并
        tracker.update(current_step, "开始表格合并...")
        
        html_table_merge_keys = []
        for key, result in element_merge_detect_result.items():
            page_1, page_2 = key
            for elem_idx_1, elem_idx_2 in result:
                text_1 = page_to_markdown_result[page_1][elem_idx_1]
                text_2 = page_to_markdown_result[page_2][elem_idx_2]
                if (text_1.startswith("<table>") and text_1.endswith("</table>") and 
                    text_2.startswith("<table>") and text_2.endswith("</table>")):
                    html_table_merge_keys.append((page_1, page_2, elem_idx_1, elem_idx_2))

        html_table_merge_result = await process_table_merge_with_progress(
            url, model, page_to_markdown_result, html_table_merge_keys, 
            max_page_retries, tracker, current_step
        )
        
        current_step += 1
        tracker.update(current_step, "构建最终文档...")
        
        # 构建最终文档
        document_text = bulid_document_text(
            page_to_markdown_result, 
            element_merge_detect_result, 
            html_table_merge_result
        )
        
        tracker.update(total_steps, "文档解析完成")
        
        return {
            "orig_path": file_path,
            "num_pages": num_pages,
            "document_text": document_text,
            "page_texts": page_texts,
            "fallback_pages": fallback_pages,
        }
        
    except Exception as e:
        logger.error(f"解析失败: {e}")
        if progress_callback:
            progress_callback(0, f"解析失败: {str(e)}")
        return None

async def process_task_with_progress(
    url: str, 
    model: str, 
    task_name: str, 
    task_args, 
    max_retries: int = 1,
    progress_callback: Optional[Callable] = None
):
    """带进度跟踪的任务处理"""
    if progress_callback:
        progress_callback(0, f"开始 {task_name} 任务")
    
    result = await process_task(url, model, task_name, task_args, max_retries)
    
    if progress_callback:
        if result is not None:
            progress_callback(1, f"{task_name} 任务完成")
        else:
            progress_callback(0, f"{task_name} 任务失败")
    
    return result

async def process_table_merge_with_progress(
    url: str,
    model: str, 
    page_to_markdown_result: Dict,
    html_table_merge_keys: list,
    max_retries: int,
    tracker: ProgressTracker,
    current_step: int
) -> Dict:
    """带进度跟踪的表格合并处理"""
    import copy
    
    html_table_merge_result = {}
    page_to_markdown_result_tmp = copy.deepcopy(page_to_markdown_result)
    
    html_table_merge_keys = sorted(html_table_merge_keys, key=lambda x: -x[0])
    
    i = 0
    while i < len(html_table_merge_keys):
        tmp = set()
        keys = []
        while i < len(html_table_merge_keys):
            page_1, page_2, elem_idx_1, elem_idx_2 = html_table_merge_keys[i]
            if (page_2, elem_idx_2) in tmp:
                break
            tmp.add((page_1, elem_idx_1))
            keys.append((page_1, page_2, elem_idx_1, elem_idx_2))
            i += 1

        if keys:
            tracker.update(current_step, f"合并 {len(keys)} 个表格...")
            
            html_table_merge_tasks = []
            async with asyncio.TaskGroup() as tg:
                for page_1, page_2, elem_idx_1, elem_idx_2 in keys:
                    task = tg.create_task(
                        process_task(
                            url, model, 'html_table_merge',
                            (page_to_markdown_result_tmp[page_1][elem_idx_1], 
                             page_to_markdown_result_tmp[page_2][elem_idx_2]),
                            max_retries
                        )
                    )
                    html_table_merge_tasks.append(task)
                    
            results = [task.result() for task in html_table_merge_tasks]
            for key, result in zip(keys, results):
                if result is not None:
                    html_table_merge_result[key] = result
                    page_1, page_2, elem_idx_1, elem_idx_2 = key
                    page_to_markdown_result_tmp[page_1][elem_idx_1] = result
    
    return html_table_merge_result

async def stream_parse_events(
    url: str,
    model: str,
    file_path: str,
    skip_cross_page_merge: bool = False,
    max_page_retries: int = 1
) -> AsyncGenerator[Dict[str, Any], None]:
    """
    生成流式解析事件
    
    Yields:
        包含事件类型和数据的字典
    """
    def progress_callback(progress: float, message: str):
        return {
            "event": "progress",
            "data": {
                "progress": progress,
                "message": message
            }
        }
    
    try:
        yield {
            "event": "start",
            "data": {
                "message": f"开始解析文档: {file_path}",
                "file_path": file_path
            }
        }
        
        # 创建一个队列来收集进度事件
        progress_queue = asyncio.Queue()
        
        def queue_progress(progress: float, message: str):
            try:
                progress_queue.put_nowait(progress_callback(progress, message))
            except asyncio.QueueFull:
                pass  # 忽略队列满的情况
        
        # 启动解析任务
        parse_task = asyncio.create_task(
            parse_with_progress(
                url, model, file_path, skip_cross_page_merge, 
                max_page_retries, queue_progress
            )
        )
        
        # 处理进度事件
        while not parse_task.done():
            try:
                # 等待进度事件或任务完成
                event = await asyncio.wait_for(progress_queue.get(), timeout=0.1)
                yield event
            except asyncio.TimeoutError:
                continue  # 继续检查任务状态
        
        # 获取最终结果
        result = await parse_task
        
        if result is None:
            yield {
                "event": "error",
                "data": {"error": "文档解析失败"}
            }
        else:
            yield {
                "event": "complete",
                "data": {
                    "result": result,
                    "message": "文档解析完成"
                }
            }
            
    except Exception as e:
        logger.error(f"流式解析失败: {e}")
        yield {
            "event": "error",
            "data": {"error": str(e)}
        }
