#!/usr/bin/env python3
"""
OCRFlux MCP 服务器远程URL功能使用示例
"""

import asyncio
from ocrflux_mcp_server import ocr_parse_document, ocr_parse_document_stream


async def example_parse_remote_pdf():
    """示例：解析远程PDF文件"""
    
    # 示例远程PDF文件URL
    remote_pdf_url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
    
    print("=" * 60)
    print("示例1：解析远程PDF文件")
    print("=" * 60)
    print(f"远程文件URL: {remote_pdf_url}")
    
    try:
        # 使用新的 file_url 参数
        result = await ocr_parse_document(
            file_url=remote_pdf_url,
            filename="dummy.pdf",  # 可选，用于确定文件类型
            skip_cross_page_merge=False,
            max_page_retries=1
        )
        
        if "error" in result:
            print(f"解析失败: {result['error']}")
        else:
            print("解析成功！")
            print(f"消息: {result['message']}")
            print(f"文档页数: {result['result'].get('num_pages', 'N/A')}")
            print(f"文档内容长度: {len(result['result'].get('document_text', ''))} 字符")
            
            # 显示前200个字符的内容
            content = result['result'].get('document_text', '')
            if content:
                print(f"内容预览: {content[:200]}...")
                
    except Exception as e:
        print(f"发生错误: {e}")


async def example_parse_remote_image():
    """示例：解析远程图片文件"""
    
    # 示例远程图片URL（包含文字的图片）
    remote_image_url = "http://minio-test.adas.com:9000/tongji/shanghai_citizen_hotline/20250619/8851715978853376__0.png"
    
    print("\n" + "=" * 60)
    print("示例2：解析远程图片文件")
    print("=" * 60)
    print(f"远程文件URL: {remote_image_url}")
    
    try:
        # 使用新的 file_url 参数
        result = await ocr_parse_document(
            file_url=remote_image_url,
            filename="test_image.png",
            skip_cross_page_merge=False,
            max_page_retries=1
        )
        
        if "error" in result:
            print(f"解析失败: {result['error']}")
        else:
            print("解析成功！")
            print(f"消息: {result['message']}")
            print(f"文档页数: {result['result'].get('num_pages', 'N/A')}")
            print(f"文档内容长度: {len(result['result'].get('document_text', ''))} 字符")
            
            # 显示内容
            content = result['result'].get('document_text', '')
            if content:
                print(f"识别的文字: {content}")
                
    except Exception as e:
        print(f"发生错误: {e}")


async def example_stream_parse_remote():
    """示例：流式解析远程文件"""
    
    remote_url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
    
    print("\n" + "=" * 60)
    print("示例3：流式解析远程文件")
    print("=" * 60)
    print(f"远程文件URL: {remote_url}")
    
    try:
        # 创建流式解析任务
        result = await ocr_parse_document_stream(
            file_url=remote_url,
            filename="dummy.pdf",
            skip_cross_page_merge=False,
            max_page_retries=1
        )
        
        if "error" in result:
            print(f"创建流式任务失败: {result['error']}")
        else:
            print("流式任务创建成功！")
            print(f"任务ID: {result['task_id']}")
            print(f"流URL: {result['stream_url']}")
            print("注意：实际的流式处理需要通过MCP服务器的SSE端点访问")
            
    except Exception as e:
        print(f"发生错误: {e}")


async def example_error_handling():
    """示例：错误处理"""
    
    print("\n" + "=" * 60)
    print("示例4：错误处理")
    print("=" * 60)
    
    # 测试无效URL
    invalid_url = "https://invalid-domain-that-does-not-exist.com/file.pdf"
    print(f"测试无效URL: {invalid_url}")
    
    try:
        result = await ocr_parse_document(
            file_url=invalid_url,
            filename="invalid.pdf"
        )
        
        if "error" in result:
            print(f"正确处理了错误: {result['error']}")
        else:
            print("意外：没有返回错误")
            
    except Exception as e:
        print(f"捕获到异常: {e}")


async def main():
    """主函数"""
    print("OCRFlux MCP 服务器 - 远程URL功能示例")
    print("注意：这些示例需要VLLM服务正在运行")
    print("如果VLLM服务未运行，解析步骤会失败，但文件下载功能仍会正常工作")
    
    # 运行所有示例
    await example_parse_remote_pdf()
    await example_parse_remote_image()
    await example_stream_parse_remote()
    await example_error_handling()
    
    print("\n" + "=" * 60)
    print("所有示例完成")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
