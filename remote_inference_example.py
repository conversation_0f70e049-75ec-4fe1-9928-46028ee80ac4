#!/usr/bin/env python3
"""
远程VLLM服务推理示例

这个示例展示了如何使用修改后的OCRFlux来访问远程VLLM服务，
而不是在本地启动模型。

使用前请确保：
1. 远程VLLM服务已经启动并运行
2. 服务地址和端口正确配置
3. 模型名称与服务端一致
"""

import asyncio
from ocrflux.inference import RemoteVLLMClient, parse, parse_async

def main():
    """主函数示例"""
    
    # 配置远程服务参数
    VLLM_URL = "http://192.168.88.32:8010"  # 修改为你的VLLM服务地址
    MODEL_NAME = "OCRFlux-3B"    # 修改为你的模型名称
    
    # 测试文件路径
    test_file = r'C:\Users\<USER>\Desktop\demo.pdf'  # 修改为你的测试文件路径
    
    print("=== OCRFlux 远程VLLM服务推理示例 ===\n")
    
    # 方式1：使用RemoteVLLMClient类（推荐）
    print("1. 使用RemoteVLLMClient类:")
    try:
        client = RemoteVLLMClient(VLLM_URL, MODEL_NAME)
        result = client.parse(
            file_path=test_file,
            skip_cross_page_merge=False,  # 是否跳过跨页合并
            max_page_retries=3           # 最大重试次数
        )
        
        if result is not None:
            print(f"✓ 解析成功!")
            print(f"  - 文档路径: {result['orig_path']}")
            print(f"  - 页数: {result['num_pages']}")
            print(f"  - 失败页数: {len(result['fallback_pages'])}")
            print(f"  - 文档长度: {len(result['document_text'])} 字符")
            
            # 保存结果
            with open('output_client.md', 'w', encoding='utf-8') as f:
                f.write(result['document_text'])
            print(f"  - 结果已保存到: output_client.md")
        else:
            print("✗ 解析失败")
            
    except Exception as e:
        print(f"✗ 错误: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 方式2：直接使用parse函数
    print("2. 直接使用parse函数:")
    try:
        result = parse(
            url=VLLM_URL,
            model=MODEL_NAME,
            file_path=test_file,
            skip_cross_page_merge=False,
            max_page_retries=3
        )
        
        if result is not None:
            print(f"✓ 解析成功!")
            print(f"  - 文档路径: {result['orig_path']}")
            print(f"  - 页数: {result['num_pages']}")
            print(f"  - 失败页数: {len(result['fallback_pages'])}")
            print(f"  - 文档长度: {len(result['document_text'])} 字符")
            
            # 保存结果
            with open('output_function.md', 'w', encoding='utf-8') as f:
                f.write(result['document_text'])
            print(f"  - 结果已保存到: output_function.md")
        else:
            print("✗ 解析失败")
            
    except Exception as e:
        print(f"✗ 错误: {e}")

async def async_main():
    """异步版本示例"""
    
    # 配置远程服务参数
    VLLM_URL = "http://localhost:30024"
    MODEL_NAME = "ChatDOC/OCRFlux-3B"
    test_file = "test.pdf"
    
    print("\n" + "="*50 + "\n")
    print("3. 使用异步parse_async函数:")
    
    try:
        result = await parse_async(
            url=VLLM_URL,
            model=MODEL_NAME,
            file_path=test_file,
            skip_cross_page_merge=False,
            max_page_retries=3
        )
        
        if result is not None:
            print(f"✓ 异步解析成功!")
            print(f"  - 文档路径: {result['orig_path']}")
            print(f"  - 页数: {result['num_pages']}")
            print(f"  - 失败页数: {len(result['fallback_pages'])}")
            print(f"  - 文档长度: {len(result['document_text'])} 字符")
            
            # 保存结果
            with open('output_async.md', 'w', encoding='utf-8') as f:
                f.write(result['document_text'])
            print(f"  - 结果已保存到: output_async.md")
        else:
            print("✗ 异步解析失败")
            
    except Exception as e:
        print(f"✗ 异步错误: {e}")

def batch_processing_example():
    """批量处理示例"""
    
    VLLM_URL = "http://localhost:30024"
    MODEL_NAME = "ChatDOC/OCRFlux-3B"
    
    # 要处理的文件列表
    file_list = [
        "document1.pdf",
        "document2.pdf", 
        "document3.pdf"
    ]
    
    print("\n" + "="*50 + "\n")
    print("4. 批量处理示例:")
    
    client = RemoteVLLMClient(VLLM_URL, MODEL_NAME)
    
    for i, file_path in enumerate(file_list, 1):
        print(f"\n处理文件 {i}/{len(file_list)}: {file_path}")
        try:
            result = client.parse(file_path, max_page_retries=2)
            if result is not None:
                output_file = f"output_batch_{i}.md"
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(result['document_text'])
                print(f"  ✓ 成功，保存到: {output_file}")
            else:
                print(f"  ✗ 失败")
        except Exception as e:
            print(f"  ✗ 错误: {e}")

if __name__ == "__main__":
    # 运行同步示例
    main()
    
    # 运行异步示例
    # asyncio.run(async_main())
    
    # 运行批量处理示例（注释掉，因为可能没有这些文件）
    # batch_processing_example()
    
    print("\n" + "="*50)
    print("示例运行完成!")
    print("请根据你的实际情况修改VLLM_URL、MODEL_NAME和文件路径。")
