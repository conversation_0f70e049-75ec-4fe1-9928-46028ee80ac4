#!/usr/bin/env python3
"""
OCRFlux FastMCP 服务器启动脚本
支持从配置文件、环境变量和命令行参数加载配置
"""

import argparse
import asyncio
import logging
import os
import sys

from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stderr,
)

logger = logging.getLogger(__name__)

async def run_mcp_server():
    """运行 MCP 服务器"""
    # 导入并运行服务器
    from ocrflux_mcp_server import run_mcp_server as run_server
    await run_server()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OCRFlux FastMCP 服务器")
    parser.add_argument(
        "--config", 
        default="config.yaml",
        help="配置文件路径"
    )
    parser.add_argument(
        "--transport",
        choices=["sse", "stdio"],
        help="传输协议 (覆盖配置文件)"
    )
    parser.add_argument(
        "--vllm-url",
        help="VLLM 服务 URL (覆盖配置文件)"
    )
    parser.add_argument(
        "--model",
        help="模型名称 (覆盖配置文件)"
    )
    parser.add_argument(
        "--max-page-retries",
        type=int,
        help="每页最大重试次数 (覆盖配置文件)"
    )
    parser.add_argument(
        "--skip-cross-page-merge",
        action="store_true",
        help="跳过跨页合并 (覆盖配置文件)"
    )
    parser.add_argument(
        "--host",
        help="MCP 服务器监听地址"
    )
    parser.add_argument(
        "--port",
        type=int,
        help="MCP 服务器监听端口"
    )

    args = parser.parse_args()
    
    # 构建服务器参数
    server_args = []
    
    if args.config:
        server_args.extend(['--config', args.config])
    
    if args.transport:
        server_args.extend(['--transport', args.transport])
    
    if args.vllm_url:
        server_args.extend(['--vllm-url', args.vllm_url])
    
    if args.model:
        server_args.extend(['--model', args.model])
    
    if args.max_page_retries is not None:
        server_args.extend(['--max-page-retries', str(args.max_page_retries)])
    
    if args.skip_cross_page_merge:
        server_args.append('--skip-cross-page-merge')
    
    if args.host:
        server_args.extend(['--host', args.host])
    
    if args.port is not None:
        server_args.extend(['--port', str(args.port)])
    
    # 设置命令行参数
    sys.argv = ['ocrflux_mcp_server.py'] + server_args
    
    logger.info("启动 OCRFlux FastMCP 服务器...")
    
    try:
        # 运行服务器
        asyncio.run(run_mcp_server())
    except KeyboardInterrupt:
        logger.info("服务器被用户中断")
    except Exception as e:
        logger.error(f"服务器运行失败: {e}")
        raise

if __name__ == "__main__":
    main()
