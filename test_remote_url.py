#!/usr/bin/env python3
"""
测试远程URL文件下载功能
"""

import asyncio
import tempfile
import os
from ocrflux_mcp_server import download_remote_file


async def test_download_remote_file():
    """测试远程文件下载功能"""
    
    # 测试一个公开的PDF文件URL（示例）
    test_url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
    
    try:
        print(f"开始测试下载远程文件: {test_url}")
        
        # 下载文件
        temp_path = await download_remote_file(test_url, "test.pdf")
        
        print(f"下载成功，临时文件路径: {temp_path}")
        
        # 检查文件是否存在
        if os.path.exists(temp_path):
            file_size = os.path.getsize(temp_path)
            print(f"文件大小: {file_size} 字节")
            
            # 清理临时文件
            os.unlink(temp_path)
            print("临时文件已清理")
            
            return True
        else:
            print("错误：文件未创建")
            return False
            
    except Exception as e:
        print(f"测试失败: {e}")
        return False


async def test_invalid_url():
    """测试无效URL的处理"""
    
    invalid_url = "https://invalid-url-that-does-not-exist.com/file.pdf"
    
    try:
        print(f"测试无效URL: {invalid_url}")
        temp_path = await download_remote_file(invalid_url, "invalid.pdf")
        print("错误：应该抛出异常但没有")
        return False
        
    except Exception as e:
        print(f"正确处理了无效URL: {e}")
        return True


async def main():
    """主测试函数"""
    print("=" * 50)
    print("测试远程文件下载功能")
    print("=" * 50)
    
    # 测试1：有效URL
    print("\n测试1：下载有效的远程文件")
    test1_result = await test_download_remote_file()
    
    # 测试2：无效URL
    print("\n测试2：处理无效URL")
    test2_result = await test_invalid_url()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"测试1 (有效URL): {'通过' if test1_result else '失败'}")
    print(f"测试2 (无效URL): {'通过' if test2_result else '失败'}")
    
    if test1_result and test2_result:
        print("所有测试通过！")
    else:
        print("部分测试失败")


if __name__ == "__main__":
    asyncio.run(main())
